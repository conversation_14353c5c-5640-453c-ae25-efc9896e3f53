<?php
// File untuk mengecek dan menginstall tabel patroli secara otomatis
require_once'../../../sw-library/sw-config.php';

// Function to check if table exists
function tableExists($connection, $tableName) {
    $result = $connection->query("SHOW TABLES LIKE '$tableName'");
    return $result && $result->num_rows > 0;
}

// Function to create patroli table
function createPatroliTable($connection) {
    $sql = "
    CREATE TABLE `patroli` (
      `patroli_id` int(11) NOT NULL AUTO_INCREMENT,
      `employees_id` int(11) NOT NULL COMMENT 'ID karyawan yang melakukan patroli',
      `building_id` int(11) NOT NULL COMMENT 'ID lokasi patroli',
      `dokumentasi` varchar(200) NOT NULL COMMENT 'File foto/video dokumentasi patroli',
      `tanggal` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT 'Tanggal dan waktu patroli dilakukan',
      `status` varchar(20) NOT NULL DEFAULT 'pending' COMMENT 'Status patroli: pending, approved, rejected, completed',
      `komentar` text NULL COMMENT 'Komentar dari operator atau karyawan',
      `rating` int(1) NULL COMMENT 'Rating patroli 1-5, null jika belum dinilai',
      `checklist_id` int(11) NULL COMMENT 'ID checklist untuk bagian operator',
      PRIMARY KEY (`patroli_id`),
      KEY `idx_employees_id` (`employees_id`),
      KEY `idx_building_id` (`building_id`),
      KEY `idx_tanggal` (`tanggal`),
      KEY `idx_status` (`status`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
    ";
    
    return $connection->query($sql);
}

// Function to add foreign key constraints
function addConstraints($connection) {
    $sql = "
    ALTER TABLE `patroli`
      ADD CONSTRAINT `fk_patroli_employees` FOREIGN KEY (`employees_id`) REFERENCES `employees` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
      ADD CONSTRAINT `fk_patroli_building` FOREIGN KEY (`building_id`) REFERENCES `building` (`building_id`) ON DELETE CASCADE ON UPDATE CASCADE;
    ";
    
    return $connection->query($sql);
}

// Function to create upload directory
function createUploadDir() {
    $upload_dir = '../../../sw-content/patroli/';
    if(!is_dir($upload_dir)) {
        if(mkdir($upload_dir, 0755, true)) {
            // Create index.html for security
            $index_content = '<!DOCTYPE html>
<html>
<head>
    <title>403 Forbidden</title>
</head>
<body>
    <h1>Directory access is forbidden.</h1>
</body>
</html>';
            file_put_contents($upload_dir . 'index.html', $index_content);
            return true;
        }
        return false;
    }
    return true;
}

// Main installation process
$response = array('success' => false, 'message' => '');

try {
    // Check if table exists
    if(tableExists($connection, 'patroli')) {
        $response['success'] = true;
        $response['message'] = 'Tabel patroli sudah ada dan siap digunakan.';
    } else {
        // Create table
        if(createPatroliTable($connection)) {
            // Add constraints (ignore errors if already exist)
            addConstraints($connection);
            
            // Create upload directory
            if(createUploadDir()) {
                $response['success'] = true;
                $response['message'] = 'Tabel patroli berhasil dibuat dan siap digunakan!';
            } else {
                $response['success'] = false;
                $response['message'] = 'Tabel dibuat tapi gagal membuat folder upload.';
            }
        } else {
            $response['success'] = false;
            $response['message'] = 'Gagal membuat tabel patroli: ' . $connection->error;
        }
    }
} catch (Exception $e) {
    $response['success'] = false;
    $response['message'] = 'Error: ' . $e->getMessage();
}

// Return JSON response for AJAX calls
if(isset($_GET['ajax'])) {
    header('Content-Type: application/json');
    echo json_encode($response);
    exit;
}

// Return result for direct access
echo $response['success'] ? 'SUCCESS: ' : 'ERROR: ';
echo $response['message'];
?>
