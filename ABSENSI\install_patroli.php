<?php
// Script untuk menginstall tabel patroli
// Jalankan file ini sekali untuk membuat tabel patroli

require_once 'sw-library/sw-config.php';

echo "<h2>🛡️ Instalasi Tabel Patroli</h2>";

// Check if table already exists
$check = $connection->query("SHOW TABLES LIKE 'patroli'");
if($check && $check->num_rows > 0) {
    echo "<div style='color: green; padding: 10px; border: 1px solid green; background: #f0fff0;'>";
    echo "✅ Tabel 'patroli' sudah ada di database!";
    echo "</div>";
    echo "<p><a href='sw-admin/'>← Kembali ke Admin Panel</a></p>";
    exit;
}

// Create table
$sql = "
CREATE TABLE `patroli` (
  `patroli_id` int(11) NOT NULL AUTO_INCREMENT,
  `employees_id` int(11) NOT NULL COMMENT 'ID karyawan yang melakukan patroli',
  `building_id` int(11) NOT NULL COMMENT 'ID lokasi patroli',
  `dokumentasi` varchar(200) NOT NULL COMMENT 'File foto/video dokumentasi patroli',
  `tanggal` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT 'Tanggal dan waktu patroli dilakukan',
  `status` varchar(20) NOT NULL DEFAULT 'pending' COMMENT 'Status patroli: pending, approved, rejected, completed',
  `komentar` text NULL COMMENT 'Komentar dari operator atau karyawan',
  `rating` int(1) NULL COMMENT 'Rating patroli 1-5, null jika belum dinilai',
  `checklist_id` int(11) NULL COMMENT 'ID checklist untuk bagian operator',
  PRIMARY KEY (`patroli_id`),
  KEY `idx_employees_id` (`employees_id`),
  KEY `idx_building_id` (`building_id`),
  KEY `idx_tanggal` (`tanggal`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
";

if($connection->query($sql)) {
    echo "<div style='color: green; padding: 10px; border: 1px solid green; background: #f0fff0;'>";
    echo "✅ Tabel 'patroli' berhasil dibuat!<br>";
    
    // Add foreign key constraints
    $fk_sql = "
    ALTER TABLE `patroli`
      ADD CONSTRAINT `fk_patroli_employees` FOREIGN KEY (`employees_id`) REFERENCES `employees` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
      ADD CONSTRAINT `fk_patroli_building` FOREIGN KEY (`building_id`) REFERENCES `building` (`building_id`) ON DELETE CASCADE ON UPDATE CASCADE;
    ";
    
    if($connection->query($fk_sql)) {
        echo "✅ Foreign key constraints berhasil ditambahkan!<br>";
    } else {
        echo "⚠️ Warning: Foreign key constraints gagal ditambahkan: " . $connection->error . "<br>";
    }
    
    // Create upload directory
    $upload_dir = 'sw-content/patroli/';
    if(!is_dir($upload_dir)) {
        if(mkdir($upload_dir, 0755, true)) {
            echo "✅ Folder upload berhasil dibuat: $upload_dir<br>";
            
            // Create index.html for security
            $index_content = '<!DOCTYPE html>
<html>
<head>
    <title>403 Forbidden</title>
</head>
<body>
    <h1>Directory access is forbidden.</h1>
</body>
</html>';
            file_put_contents($upload_dir . 'index.html', $index_content);
            echo "✅ File keamanan index.html dibuat<br>";
        } else {
            echo "⚠️ Warning: Gagal membuat folder upload<br>";
        }
    } else {
        echo "✅ Folder upload sudah ada<br>";
    }
    
    echo "</div>";
    echo "<h3>🎉 Instalasi Selesai!</h3>";
    echo "<p>Fitur patroli sekarang sudah siap digunakan.</p>";
    echo "<p><strong>Akses:</strong></p>";
    echo "<ul>";
    echo "<li><a href='sw-admin/' target='_blank'>Admin Panel</a> → Menu 'Data Patroli'</li>";
    echo "<li>Login sebagai Administrator atau Operator</li>";
    echo "</ul>";
    
} else {
    echo "<div style='color: red; padding: 10px; border: 1px solid red; background: #fff0f0;'>";
    echo "❌ Gagal membuat tabel 'patroli'!<br>";
    echo "Error: " . $connection->error;
    echo "</div>";
    echo "<p><strong>Solusi:</strong></p>";
    echo "<ul>";
    echo "<li>Pastikan database 'absensi_sigap' sudah ada</li>";
    echo "<li>Pastikan user database memiliki permission CREATE TABLE</li>";
    echo "<li>Periksa konfigurasi database di sw-library/sw-config.php</li>";
    echo "</ul>";
}

echo "<hr>";
echo "<p><a href='sw-admin/'>← Kembali ke Admin Panel</a></p>";
echo "<p><small>File: install_patroli.php | " . date('Y-m-d H:i:s') . "</small></p>";
?>

<style>
body {
    font-family: Arial, sans-serif;
    max-width: 800px;
    margin: 50px auto;
    padding: 20px;
    background: #f5f5f5;
}
div {
    margin: 10px 0;
    border-radius: 5px;
}
a {
    color: #007cba;
    text-decoration: none;
}
a:hover {
    text-decoration: underline;
}
</style>
