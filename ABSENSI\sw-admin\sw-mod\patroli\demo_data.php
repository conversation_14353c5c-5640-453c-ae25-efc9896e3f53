<?php
// File untuk menambahkan data demo patroli
session_start();
if(empty($_SESSION['SESSION_USER']) && empty($_SESSION['SESSION_ID'])){
    header('location:../../login/');
    exit;
}

require_once'../../../sw-library/sw-config.php';
require_once'../../login/login_session.php';

// Check if user is admin
if($level_user != 1) {
    echo '<script>alert("Akses ditolak! Hanya admin yang dapat menambah data demo."); window.location="../../patroli";</script>';
    exit;
}

echo "<h2>🛡️ Demo Data Patroli</h2>";

// Check if table exists
$check_table = "SHOW TABLES LIKE 'patroli'";
$table_exists = $connection->query($check_table);

if(!$table_exists || $table_exists->num_rows == 0) {
    echo "<div style='color: red; padding: 10px; border: 1px solid red; background: #fff0f0;'>";
    echo "❌ Tabel patroli belum ada! <PERSON><PERSON><PERSON> install terlebih dahulu.";
    echo "</div>";
    echo "<p><a href='install_table.php'>Install Tabel Patroli</a></p>";
    exit;
}

// Check if demo data already exists
$check_demo = $connection->query("SELECT COUNT(*) as count FROM patroli");
$demo_count = $check_demo->fetch_assoc()['count'];

if($demo_count > 0) {
    echo "<div style='color: orange; padding: 10px; border: 1px solid orange; background: #fff8e1;'>";
    echo "⚠️ Sudah ada $demo_count data patroli di database.";
    echo "</div>";
    echo "<p>Apakah Anda ingin menambah data demo lagi?</p>";
    echo "<p><a href='?add=yes' class='btn'>Ya, Tambah Data Demo</a> | <a href='../../patroli'>Kembali ke Patroli</a></p>";
    
    if(!isset($_GET['add'])) {
        exit;
    }
}

// Get employees and buildings for demo data
$employees = [];
$buildings = [];

$emp_query = "SELECT id, employees_name FROM employees LIMIT 5";
$emp_result = $connection->query($emp_query);
while($row = $emp_result->fetch_assoc()) {
    $employees[] = $row;
}

$building_query = "SELECT building_id, name FROM building LIMIT 3";
$building_result = $connection->query($building_query);
while($row = $building_result->fetch_assoc()) {
    $buildings[] = $row;
}

if(empty($employees) || empty($buildings)) {
    echo "<div style='color: red; padding: 10px; border: 1px solid red; background: #fff0f0;'>";
    echo "❌ Tidak ada data karyawan atau lokasi! Silakan tambahkan data master terlebih dahulu.";
    echo "</div>";
    exit;
}

// Demo data
$demo_data = [
    [
        'employees_id' => $employees[0]['id'],
        'building_id' => $buildings[0]['building_id'],
        'dokumentasi' => 'demo-patroli-1.jpg',
        'status' => 'completed',
        'rating' => 5,
        'komentar' => 'Patroli rutin pagi hari berjalan lancar. Area aman dan bersih.',
        'tanggal' => date('Y-m-d H:i:s', strtotime('-2 hours'))
    ],
    [
        'employees_id' => $employees[1]['id'] ?? $employees[0]['id'],
        'building_id' => $buildings[1]['building_id'] ?? $buildings[0]['building_id'],
        'dokumentasi' => 'demo-patroli-2.jpg',
        'status' => 'approved',
        'rating' => 4,
        'komentar' => 'Patroli keamanan malam. Ditemukan beberapa lampu mati di area parkir.',
        'tanggal' => date('Y-m-d H:i:s', strtotime('-1 hour'))
    ],
    [
        'employees_id' => $employees[2]['id'] ?? $employees[0]['id'],
        'building_id' => $buildings[0]['building_id'],
        'dokumentasi' => 'demo-patroli-3.jpg',
        'status' => 'pending',
        'rating' => null,
        'komentar' => 'Patroli siang hari. Menunggu verifikasi dari supervisor.',
        'tanggal' => date('Y-m-d H:i:s', strtotime('-30 minutes'))
    ],
    [
        'employees_id' => $employees[0]['id'],
        'building_id' => $buildings[2]['building_id'] ?? $buildings[0]['building_id'],
        'dokumentasi' => 'demo-patroli-4.jpg',
        'status' => 'rejected',
        'rating' => 2,
        'komentar' => 'Dokumentasi kurang lengkap. Perlu patroli ulang.',
        'tanggal' => date('Y-m-d H:i:s', strtotime('-3 hours'))
    ],
    [
        'employees_id' => $employees[1]['id'] ?? $employees[0]['id'],
        'building_id' => $buildings[1]['building_id'] ?? $buildings[0]['building_id'],
        'dokumentasi' => 'demo-patroli-5.jpg',
        'status' => 'completed',
        'rating' => 3,
        'komentar' => 'Patroli kebersihan. Area cukup bersih namun perlu perhatian di toilet.',
        'tanggal' => date('Y-m-d H:i:s', strtotime('-10 minutes'))
    ]
];

$success_count = 0;
$latest_id = 0;

echo "<div style='color: blue; padding: 10px; border: 1px solid blue; background: #f0f8ff;'>";
echo "🔄 Menambahkan data demo...<br><br>";

foreach($demo_data as $data) {
    $rating_value = ($data['rating'] !== null) ? "'{$data['rating']}'" : "NULL";
    $komentar_value = "'{$data['komentar']}'";
    
    $query = "INSERT INTO patroli (employees_id, building_id, dokumentasi, tanggal, status, rating, komentar) 
              VALUES ('{$data['employees_id']}', '{$data['building_id']}', '{$data['dokumentasi']}', 
                      '{$data['tanggal']}', '{$data['status']}', $rating_value, $komentar_value)";
    
    if($connection->query($query)) {
        $latest_id = $connection->insert_id;
        $success_count++;
        echo "✅ Data demo $success_count berhasil ditambahkan (ID: $latest_id)<br>";
    } else {
        echo "❌ Gagal menambahkan data demo $success_count: " . $connection->error . "<br>";
    }
}

echo "</div>";

if($success_count > 0) {
    echo "<div style='color: green; padding: 10px; border: 1px solid green; background: #f0fff0;'>";
    echo "🎉 <strong>Berhasil!</strong><br>";
    echo "✅ $success_count data demo patroli berhasil ditambahkan<br>";
    echo "✅ Data terbaru akan di-highlight dengan warna hijau<br>";
    echo "✅ Sistem siap untuk digunakan<br>";
    echo "</div>";
    
    echo "<h3>📊 Data Demo yang Ditambahkan:</h3>";
    echo "<ul>";
    foreach($demo_data as $i => $data) {
        $emp_name = '';
        foreach($employees as $emp) {
            if($emp['id'] == $data['employees_id']) {
                $emp_name = $emp['employees_name'];
                break;
            }
        }
        
        $building_name = '';
        foreach($buildings as $building) {
            if($building['building_id'] == $data['building_id']) {
                $building_name = $building['name'];
                break;
            }
        }
        
        echo "<li><strong>{$emp_name}</strong> - {$building_name} - Status: {$data['status']} - Rating: " . ($data['rating'] ?? 'Belum dinilai') . "</li>";
    }
    echo "</ul>";
    
    echo "<p><a href='../../patroli?success=demo&id=$latest_id' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>🛡️ Lihat Data Patroli</a></p>";
} else {
    echo "<div style='color: red; padding: 10px; border: 1px solid red; background: #fff0f0;'>";
    echo "❌ Tidak ada data demo yang berhasil ditambahkan!";
    echo "</div>";
}

echo "<hr>";
echo "<p><a href='../../patroli'>← Kembali ke Data Patroli</a></p>";
echo "<p><small>File: demo_data.php | " . date('Y-m-d H:i:s') . "</small></p>";
?>

<style>
body {
    font-family: Arial, sans-serif;
    max-width: 800px;
    margin: 50px auto;
    padding: 20px;
    background: #f5f5f5;
}
div {
    margin: 10px 0;
    border-radius: 5px;
}
a {
    color: #007cba;
    text-decoration: none;
}
a:hover {
    text-decoration: underline;
}
.btn {
    background: #007cba;
    color: white;
    padding: 8px 16px;
    border-radius: 4px;
    text-decoration: none;
    display: inline-block;
    margin: 5px;
}
.btn:hover {
    background: #005a8b;
    text-decoration: none;
}
</style>
