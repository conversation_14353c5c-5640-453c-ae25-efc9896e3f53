-- --------------------------------------------------------
--
-- Struktur dari tabel `patroli`
-- Tabel untuk mencatat aktivitas patroli karyawan
-- Se<PERSON><PERSON> permintaan: id patroli, dokumentasi, tanggal (timestamp), status, komentar null, rating, id karyawan, id lokasi, id ceklis untuk operator
--

CREATE TABLE `patroli` (
  `patroli_id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'ID patroli (primary key)',
  `employees_id` int(11) NOT NULL COMMENT 'ID karyawan yang melakukan patroli',
  `building_id` int(11) NOT NULL COMMENT 'ID lokasi patroli (referensi ke tabel building)',
  `dokumentasi` varchar(200) NOT NULL COMMENT 'File foto/video dokumentasi patroli',
  `tanggal` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT 'Tanggal dan waktu patroli dilakukan',
  `status` varchar(20) NOT NULL DEFAULT 'pending' COMMENT 'Status patroli: pending, approved, rejected, completed',
  `komentar` text NULL COMMENT 'Komentar dari operator atau karyawan (nullable)',
  `rating` int(1) NULL COMMENT 'Rating patroli 1-5, null jika belum dinilai',
  `checklist_id` int(11) NULL COMMENT 'ID checklist untuk bagian operator (nullable)',
  PRIMARY KEY (`patroli_id`),
  KEY `idx_employees_id` (`employees_id`),
  KEY `idx_building_id` (`building_id`),
  KEY `idx_tanggal` (`tanggal`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='Tabel untuk mencatat aktivitas patroli karyawan';

-- --------------------------------------------------------
--
-- Tambahkan foreign key constraints (opsional, untuk menjaga integritas data)
--

-- Foreign key ke tabel employees
ALTER TABLE `patroli`
  ADD CONSTRAINT `fk_patroli_employees` 
  FOREIGN KEY (`employees_id`) 
  REFERENCES `employees` (`id`) 
  ON DELETE CASCADE ON UPDATE CASCADE;

-- Foreign key ke tabel building
ALTER TABLE `patroli`
  ADD CONSTRAINT `fk_patroli_building` 
  FOREIGN KEY (`building_id`) 
  REFERENCES `building` (`building_id`) 
  ON DELETE CASCADE ON UPDATE CASCADE;

-- --------------------------------------------------------
--
-- AUTO_INCREMENT untuk tabel patroli
--

ALTER TABLE `patroli`
  MODIFY `patroli_id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=1;

-- --------------------------------------------------------
--
-- Contoh data untuk testing (opsional - hapus komentar untuk menggunakan)
--

/*
INSERT INTO `patroli` (`employees_id`, `building_id`, `dokumentasi`, `status`, `komentar`, `rating`, `checklist_id`) VALUES
(17, 7, 'patroli-17-1625123456.jpg', 'pending', NULL, NULL, NULL),
(18, 8, 'patroli-18-1625123500.jpg', 'completed', 'Patroli berjalan lancar', 4, 1),
(17, 7, 'patroli-17-1625123600.jpg', 'approved', 'Area aman dan bersih', 5, 2);
*/

COMMIT;

-- --------------------------------------------------------
--
-- Penjelasan Field:
-- 
-- patroli_id: Primary key, auto increment
-- employees_id: Foreign key ke tabel employees (id karyawan)
-- building_id: Foreign key ke tabel building (id lokasi)
-- dokumentasi: Nama file foto/video dokumentasi
-- tanggal: Timestamp otomatis saat record dibuat
-- status: Status patroli (pending/approved/rejected/completed)
-- komentar: Text nullable untuk komentar
-- rating: Integer 1-5 untuk rating, nullable
-- checklist_id: Integer nullable untuk ID checklist operator
--
-- --------------------------------------------------------
