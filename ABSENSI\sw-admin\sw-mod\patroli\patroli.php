<?php
if(empty($connection)){
  header('location:../../');
} else {
  include_once 'sw-mod/sw-panel.php';
echo'
  <div class="content-wrapper">';
switch(@$_GET['op']){ 
    default:
echo'
<section class="content-header">
  <h1>Data<small> Patroli</small></h1>
    <ol class="breadcrumb">
      <li><a href="./"><i class="fa fa-dashboard"></i> Beranda</a></li>
      <li class="active">Data Patroli</li>
    </ol>
</section>';
echo'
<section class="content">
  <div class="row">
    <div class="col-xs-12 col-sm-12 col-md-12 col-lg-12">
      <div class="box box-solid">
        <div class="box-header with-border">
          <h3 class="box-title"><b>Data Patroli</b></h3>
          <div class="box-tools pull-right">';
          if($level_user==1 || $level_user==2){
            echo'
            <a href="'.$mod.'&op=add" class="btn btn-success btn-flat"><i class="fa fa-plus"></i> Tambah Baru</a>';}
          else{
            echo'<button type="button" class="btn btn-success btn-flat access-failed"><i class="fa fa-plus"></i> Tambah Baru</button>';
          }echo'
          </div>
        </div>
    <div class="box-body">
      <div class="table-responsive">
          <table id="sw-datatable" class="table table-bordered">
            <thead>
            <tr>
              <th style="width: 10px">No</th>
              <th>Karyawan</th>
              <th>Lokasi</th>
              <th>Tanggal</th>
              <th>Status</th>
              <th>Rating</th>
              <th>Dokumentasi</th>
              <th style="width:150px" class="text-center">Aksi</th>
            </tr>
            </thead>
            <tbody>';

            $query ="SELECT p.*, e.employees_name, b.name as building_name 
                     FROM patroli p 
                     LEFT JOIN employees e ON p.employees_id = e.id 
                     LEFT JOIN building b ON p.building_id = b.building_id 
                     ORDER BY p.tanggal DESC";
            $result = $connection->query($query);
            if($result->num_rows > 0){
              $no=0;
              while ($row = $result->fetch_assoc()) {
              $no++;
              $patroli_id = $row['patroli_id'];
              $employees_name = $row['employees_name'];
              $building_name = $row['building_name'];
              $tanggal = date('d-m-Y H:i', strtotime($row['tanggal']));
              $status = $row['status'];
              $rating = $row['rating'] ? $row['rating'] : '-';
              $dokumentasi = $row['dokumentasi'];
              
              // Status badge
              $status_badge = '';
              switch($status) {
                case 'pending':
                  $status_badge = '<span class="label label-warning">Menunggu</span>';
                  break;
                case 'approved':
                  $status_badge = '<span class="label label-success">Disetujui</span>';
                  break;
                case 'rejected':
                  $status_badge = '<span class="label label-danger">Ditolak</span>';
                  break;
                case 'completed':
                  $status_badge = '<span class="label label-info">Selesai</span>';
                  break;
                default:
                  $status_badge = '<span class="label label-default">'.$status.'</span>';
              }
              
              echo'
              <tr>
                <td>'.$no.'</td>
                <td>'.$employees_name.'</td>
                <td>'.$building_name.'</td>
                <td>'.$tanggal.'</td>
                <td>'.$status_badge.'</td>
                <td class="text-center">';
                if($rating != '-') {
                  echo '<div class="rating-display">';
                  for($i = 1; $i <= 5; $i++) {
                    if($i <= $rating) {
                      echo '<i class="fa fa-star" style="color: #f39c12;"></i>';
                    } else {
                      echo '<i class="fa fa-star-o" style="color: #ddd;"></i>';
                    }
                  }
                  echo '<br><small class="text-muted">('.$rating.'/5)</small>';
                  echo '</div>';
                } else {
                  echo '<span class="text-muted">Belum dinilai</span>';
                }
                echo'</td>
                <td>';
                if(!empty($dokumentasi)) {
                  echo '<a href="../sw-content/patroli/'.$dokumentasi.'" target="_blank" class="btn btn-xs btn-info">
                        <i class="fa fa-eye"></i> Lihat</a>';
                } else {
                  echo '-';
                }
                echo'</td>
                <td class="text-center">';
                  echo'<div class="btn-group">
                    <a href="'.$mod.'&op=edit&patroli_id='.$patroli_id.'" class="btn btn-warning btn-xs" title="Edit">
                      <i class="fa fa-edit"></i>
                    </a>';
                    if($level_user==1 || $level_user==2){
                      echo'<a href="'.$mod.'&op=delete&patroli_id='.$patroli_id.'" class="btn btn-danger btn-xs" title="Hapus" onclick="return confirm(\'Yakin ingin menghapus data ini?\')">
                        <i class="fa fa-trash"></i>
                      </a>';
                    }
                  echo'</div>
                </td>
              </tr>';
              }
            } else {
              echo '<tr><td colspan="8" class="text-center">Tidak ada data patroli</td></tr>';
            }
echo'
            </tbody>
          </table>
        </div>
      </div>
    </div>
  </div>
</div>
</section>';
break;

case 'add':
echo'
<section class="content-header">
  <h1>Tambah<small> Data Patroli</small></h1>
    <ol class="breadcrumb">
      <li><a href="./"><i class="fa fa-dashboard"></i> Beranda</a></li>
      <li><a href="./'.$mod.'">Data Patroli</a></li>
      <li class="active">Tambah Data</li>
    </ol>
</section>

<section class="content">
  <div class="row">
    <div class="col-md-12">
      <div class="box box-primary">
        <div class="box-header with-border">
          <h3 class="box-title">Form Tambah Patroli</h3>
        </div>
        <form class="form-horizontal" method="POST" action="sw-mod/'.$mod.'/proses.php?action=add" enctype="multipart/form-data">
          <div class="box-body">
            
            <div class="form-group">
              <label for="employees_id" class="col-sm-2 control-label">Karyawan</label>
              <div class="col-sm-10">
                <select class="form-control" name="employees_id" required>
                  <option value="">-- Pilih Karyawan --</option>';
                  $query_emp = "SELECT id, employees_name FROM employees ORDER BY employees_name";
                  $result_emp = $connection->query($query_emp);
                  while($row_emp = $result_emp->fetch_assoc()) {
                    echo '<option value="'.$row_emp['id'].'">'.$row_emp['employees_name'].'</option>';
                  }
                echo'</select>
              </div>
            </div>

            <div class="form-group">
              <label for="building_id" class="col-sm-2 control-label">Lokasi</label>
              <div class="col-sm-10">
                <select class="form-control" name="building_id" required>
                  <option value="">-- Pilih Lokasi --</option>';
                  $query_building = "SELECT building_id, name FROM building ORDER BY name";
                  $result_building = $connection->query($query_building);
                  while($row_building = $result_building->fetch_assoc()) {
                    echo '<option value="'.$row_building['building_id'].'">'.$row_building['name'].'</option>';
                  }
                echo'</select>
              </div>
            </div>

            <div class="form-group">
              <label for="dokumentasi" class="col-sm-2 control-label">Dokumentasi</label>
              <div class="col-sm-10">
                <input type="file" class="form-control" name="dokumentasi" accept="image/*,video/*" required>
                <small class="help-block">Upload foto atau video dokumentasi patroli (Max: 5MB)</small>
              </div>
            </div>

            <div class="form-group">
              <label for="status" class="col-sm-2 control-label">Status</label>
              <div class="col-sm-10">
                <select class="form-control" name="status" required>
                  <option value="pending">Menunggu</option>
                  <option value="approved">Disetujui</option>
                  <option value="rejected">Ditolak</option>
                  <option value="completed">Selesai</option>
                </select>
              </div>
            </div>

            <div class="form-group">
              <label for="rating" class="col-sm-2 control-label">Rating</label>
              <div class="col-sm-10">
                <select class="form-control" name="rating" id="rating-select">
                  <option value="">-- Belum Dinilai --</option>
                  <option value="1">⭐ 1 - Sangat Buruk</option>
                  <option value="2">⭐⭐ 2 - Buruk</option>
                  <option value="3">⭐⭐⭐ 3 - Cukup</option>
                  <option value="4">⭐⭐⭐⭐ 4 - Baik</option>
                  <option value="5">⭐⭐⭐⭐⭐ 5 - Sangat Baik</option>
                </select>
                <div class="rating-preview mt-2" style="display: none;">
                  <span class="rating-stars"></span>
                </div>
              </div>
            </div>

            <div class="form-group">
              <label for="komentar" class="col-sm-2 control-label">Komentar</label>
              <div class="col-sm-10">
                <textarea class="form-control" name="komentar" rows="3" placeholder="Komentar opsional..."></textarea>
              </div>
            </div>

          </div>
          <div class="box-footer">
            <a href="./'.$mod.'" class="btn btn-default">Batal</a>
            <button type="submit" class="btn btn-primary pull-right">Simpan</button>
          </div>
        </form>
      </div>
    </div>
  </div>
</section>';
break;

case 'edit':
$patroli_id = $_GET['patroli_id'];
$query_edit = "SELECT * FROM patroli WHERE patroli_id='$patroli_id'";
$result_edit = $connection->query($query_edit);
$row_edit = $result_edit->fetch_assoc();

echo'
<section class="content-header">
  <h1>Edit<small> Data Patroli</small></h1>
    <ol class="breadcrumb">
      <li><a href="./"><i class="fa fa-dashboard"></i> Beranda</a></li>
      <li><a href="./'.$mod.'">Data Patroli</a></li>
      <li class="active">Edit Data</li>
    </ol>
</section>

<section class="content">
  <div class="row">
    <div class="col-md-12">
      <div class="box box-primary">
        <div class="box-header with-border">
          <h3 class="box-title">Form Edit Patroli</h3>
        </div>
        <form class="form-horizontal" method="POST" action="sw-mod/'.$mod.'/proses.php?action=edit" enctype="multipart/form-data">
          <input type="hidden" name="patroli_id" value="'.$row_edit['patroli_id'].'">
          <input type="hidden" name="dokumentasi_lama" value="'.$row_edit['dokumentasi'].'">
          <div class="box-body">

            <div class="form-group">
              <label for="employees_id" class="col-sm-2 control-label">Karyawan</label>
              <div class="col-sm-10">
                <select class="form-control" name="employees_id" required>
                  <option value="">-- Pilih Karyawan --</option>';
                  $query_emp = "SELECT id, employees_name FROM employees ORDER BY employees_name";
                  $result_emp = $connection->query($query_emp);
                  while($row_emp = $result_emp->fetch_assoc()) {
                    $selected = ($row_emp['id'] == $row_edit['employees_id']) ? 'selected' : '';
                    echo '<option value="'.$row_emp['id'].'" '.$selected.'>'.$row_emp['employees_name'].'</option>';
                  }
                echo'</select>
              </div>
            </div>

            <div class="form-group">
              <label for="building_id" class="col-sm-2 control-label">Lokasi</label>
              <div class="col-sm-10">
                <select class="form-control" name="building_id" required>
                  <option value="">-- Pilih Lokasi --</option>';
                  $query_building = "SELECT building_id, name FROM building ORDER BY name";
                  $result_building = $connection->query($query_building);
                  while($row_building = $result_building->fetch_assoc()) {
                    $selected = ($row_building['building_id'] == $row_edit['building_id']) ? 'selected' : '';
                    echo '<option value="'.$row_building['building_id'].'" '.$selected.'>'.$row_building['name'].'</option>';
                  }
                echo'</select>
              </div>
            </div>

            <div class="form-group">
              <label for="dokumentasi" class="col-sm-2 control-label">Dokumentasi</label>
              <div class="col-sm-10">';
                if(!empty($row_edit['dokumentasi'])) {
                  echo '<p>File saat ini: <a href="../sw-content/patroli/'.$row_edit['dokumentasi'].'" target="_blank">'.$row_edit['dokumentasi'].'</a></p>';
                }
                echo'<input type="file" class="form-control" name="dokumentasi" accept="image/*,video/*">
                <small class="help-block">Kosongkan jika tidak ingin mengubah file (Max: 5MB)</small>
              </div>
            </div>

            <div class="form-group">
              <label for="status" class="col-sm-2 control-label">Status</label>
              <div class="col-sm-10">
                <select class="form-control" name="status" required>';
                  $statuses = [
                    'pending' => 'Menunggu',
                    'approved' => 'Disetujui',
                    'rejected' => 'Ditolak',
                    'completed' => 'Selesai'
                  ];
                  foreach($statuses as $value => $label) {
                    $selected = ($value == $row_edit['status']) ? 'selected' : '';
                    echo '<option value="'.$value.'" '.$selected.'>'.$label.'</option>';
                  }
                echo'</select>
              </div>
            </div>

            <div class="form-group">
              <label for="rating" class="col-sm-2 control-label">Rating</label>
              <div class="col-sm-10">
                <select class="form-control" name="rating" id="rating-select-edit">
                  <option value="">-- Belum Dinilai --</option>';
                  for($i = 1; $i <= 5; $i++) {
                    $selected = ($i == $row_edit['rating']) ? 'selected' : '';
                    $stars = str_repeat('⭐', $i);
                    $label = '';
                    switch($i) {
                      case 1: $label = 'Sangat Buruk'; break;
                      case 2: $label = 'Buruk'; break;
                      case 3: $label = 'Cukup'; break;
                      case 4: $label = 'Baik'; break;
                      case 5: $label = 'Sangat Baik'; break;
                    }
                    echo '<option value="'.$i.'" '.$selected.'>'.$stars.' '.$i.' - '.$label.'</option>';
                  }
                echo'</select>
                <div class="rating-preview-edit mt-2" style="display: none;">
                  <span class="rating-stars"></span>
                </div>
              </div>
            </div>

            <div class="form-group">
              <label for="komentar" class="col-sm-2 control-label">Komentar</label>
              <div class="col-sm-10">
                <textarea class="form-control" name="komentar" rows="3" placeholder="Komentar opsional...">'.$row_edit['komentar'].'</textarea>
              </div>
            </div>

          </div>
          <div class="box-footer">
            <a href="./'.$mod.'" class="btn btn-default">Batal</a>
            <button type="submit" class="btn btn-primary pull-right">Update</button>
          </div>
        </form>
      </div>
    </div>
  </div>
</section>';
break;

case 'delete':
$patroli_id = $_GET['patroli_id'];
// Get file name to delete
$query_file = "SELECT dokumentasi FROM patroli WHERE patroli_id='$patroli_id'";
$result_file = $connection->query($query_file);
$row_file = $result_file->fetch_assoc();

// Delete file if exists
if(!empty($row_file['dokumentasi'])) {
  $file_path = '../sw-content/patroli/'.$row_file['dokumentasi'];
  if(file_exists($file_path)) {
    unlink($file_path);
  }
}

// Delete record
$query_delete = "DELETE FROM patroli WHERE patroli_id='$patroli_id'";
if($connection->query($query_delete)) {
  echo '<script>alert("Data berhasil dihapus!"); window.location="./'.$mod.'";</script>';
} else {
  echo '<script>alert("Gagal menghapus data!"); window.location="./'.$mod.'";</script>';
}
break;

}
echo'</div>';

// Include CSS dan JavaScript untuk patroli
echo '
<style>
.rating-display {
    line-height: 1.2;
}
.rating-display .fa-star,
.rating-display .fa-star-o {
    font-size: 14px;
    margin-right: 2px;
}
.rating-preview,
.rating-preview-edit {
    margin-top: 10px;
    padding: 8px;
    background-color: #f9f9f9;
    border-radius: 4px;
    border: 1px solid #ddd;
}
.rating-preview .fa-star,
.rating-preview .fa-star-o,
.rating-preview-edit .fa-star,
.rating-preview-edit .fa-star-o {
    margin-right: 3px;
}
.btn-group .btn {
    margin-right: 2px;
}
.table td {
    vertical-align: middle;
}
</style>
<script src="sw-mod/patroli/scripts.js"></script>';

include_once 'sw-mod/sw-footer.php';
}
?>
