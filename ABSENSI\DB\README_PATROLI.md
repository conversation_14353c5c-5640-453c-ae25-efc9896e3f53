# Dokumentasi Tabel Patroli

## 📋 Deskripsi
Tabel `patroli` telah ditambahkan ke database `absensi_sigap` untuk mencatat aktivitas patroli yang dilakukan oleh karyawan di berbagai lokasi.

## 🗂️ Struktur Tabel

### Tabel: `patroli`

| Field | Type | Null | Key | Default | Extra | Keterangan |
|-------|------|------|-----|---------|-------|------------|
| `patroli_id` | int(11) | NO | PRI | NULL | auto_increment | ID unik patroli |
| `employees_id` | int(11) | NO | MUL | NULL | | ID karyawan (FK ke employees.id) |
| `building_id` | int(11) | NO | MUL | NULL | | ID lokasi (FK ke building.building_id) |
| `dokumentasi` | varchar(200) | NO | | NULL | | Nama file foto/video dokumentasi |
| `tanggal` | timestamp | NO | MUL | CURRENT_TIMESTAMP | | Waktu patroli dilakukan |
| `status` | varchar(20) | NO | MUL | 'pending' | | Status patroli |
| `komentar` | text | YES | | NULL | | Komentar tambahan (nullable) |
| `rating` | int(1) | YES | | NULL | | Rating 1-5 (nullable) |
| `checklist_id` | int(11) | YES | | NULL | | ID checklist untuk operator (nullable) |

## 🔗 Relasi Tabel

### Foreign Keys:
- `employees_id` → `employees.id` (CASCADE DELETE/UPDATE)
- `building_id` → `building.building_id` (CASCADE DELETE/UPDATE)

### Indexes:
- PRIMARY KEY: `patroli_id`
- INDEX: `idx_employees_id` pada `employees_id`
- INDEX: `idx_building_id` pada `building_id`
- INDEX: `idx_tanggal` pada `tanggal`
- INDEX: `idx_status` pada `status`

## 📊 Status Values

Nilai yang diizinkan untuk field `status`:
- `pending` - Patroli baru dibuat, menunggu verifikasi
- `approved` - Patroli telah disetujui
- `rejected` - Patroli ditolak
- `completed` - Patroli selesai dan telah dinilai

## ⭐ Rating System

Field `rating` menggunakan skala 1-5:
- `1` - Sangat Buruk
- `2` - Buruk
- `3` - Cukup
- `4` - Baik
- `5` - Sangat Baik
- `NULL` - Belum dinilai

## 📁 File Instalasi

### File yang tersedia:
1. `add_patroli_table.sql` - Versi lengkap dengan tabel checklist
2. `patroli_table_simple.sql` - Versi sederhana hanya tabel patroli
3. `absensi_sigap.sql` - Database utama (sudah diupdate)

### Cara Instalasi:

#### Opsi 1: Import file sederhana
```sql
-- Jalankan file ini jika hanya ingin tabel patroli
SOURCE patroli_table_simple.sql;
```

#### Opsi 2: Import database lengkap
```sql
-- Backup database lama terlebih dahulu
-- Kemudian import database yang sudah diupdate
SOURCE absensi_sigap.sql;
```

#### Opsi 3: Manual via phpMyAdmin
1. Buka phpMyAdmin
2. Pilih database `absensi_sigap`
3. Klik tab "SQL"
4. Copy-paste isi file `patroli_table_simple.sql`
5. Klik "Go"

## 🔧 Contoh Penggunaan

### Insert Data Patroli Baru:
```sql
INSERT INTO patroli (employees_id, building_id, dokumentasi, status, komentar, checklist_id) 
VALUES (17, 7, 'patroli-17-1625123456.jpg', 'pending', 'Patroli rutin pagi hari', NULL);
```

### Update Status Patroli:
```sql
UPDATE patroli 
SET status = 'approved', rating = 4, komentar = 'Patroli berjalan dengan baik' 
WHERE patroli_id = 1;
```

### Query Data Patroli dengan Join:
```sql
SELECT 
    p.patroli_id,
    e.employees_name,
    b.name as building_name,
    p.tanggal,
    p.status,
    p.rating,
    p.dokumentasi
FROM patroli p
JOIN employees e ON p.employees_id = e.id
JOIN building b ON p.building_id = b.building_id
ORDER BY p.tanggal DESC;
```

### Query Statistik Patroli:
```sql
-- Jumlah patroli per status
SELECT status, COUNT(*) as jumlah 
FROM patroli 
GROUP BY status;

-- Rating rata-rata per karyawan
SELECT 
    e.employees_name,
    AVG(p.rating) as avg_rating,
    COUNT(p.patroli_id) as total_patroli
FROM patroli p
JOIN employees e ON p.employees_id = e.id
WHERE p.rating IS NOT NULL
GROUP BY e.id, e.employees_name;
```

## 🚀 Langkah Selanjutnya

Setelah tabel berhasil dibuat, Anda dapat:

1. **Membuat Interface Admin** untuk mengelola patroli
2. **Membuat Form Input** untuk karyawan mencatat patroli
3. **Membuat Dashboard** untuk monitoring patroli
4. **Implementasi Upload File** untuk dokumentasi
5. **Membuat Sistem Notifikasi** untuk status patroli

## 📞 Support

Jika ada pertanyaan atau masalah dengan implementasi tabel patroli, silakan hubungi administrator sistem.

---
*Dibuat pada: <?php echo date('d M Y H:i'); ?>*
*Database: absensi_sigap*
*Versi: 1.0*
