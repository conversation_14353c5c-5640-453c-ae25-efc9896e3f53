<IfModule mod_php5.c>
php_value short_open_tag 1
</IfModule>

# Custom 404 errors
ErrorDocument 404 /404.php
ErrorDocument 401 /401.php

RewriteRule ^404$ 404.php [L]
RewriteRule ^logout?$ login/logout.php

Options -Indexes



RewriteRule ^login/?$ login/index.php
RewriteRule ^login/forgot?.html$ login/forgot.php
RewriteRule ^logout?$ login/sw-logout.php
RewriteRule ^absensi/print$ sw-mod/absensi/print.php
RewriteRule ^laporan-harian/print$ sw-mod/laporan-harian/print.php
RewriteRule ^cuty/print$ sw-mod/cuty/print.php


RewriteCond %{REQUEST_FILENAME} !-f
RewriteRule ^(.*)$ index.php?mod=$1 [L]

<IfModule mod_gzip.c>
mod_gzip_on Yes
mod_gzip_dechunk Yes
mod_gzip_item_include file .(html?|txt|css|js|php|pl)$
mod_gzip_item_include handler ^cgi-script$
mod_gzip_item_include mime ^text/.*
mod_gzip_item_include mime ^application/x-javascript.*
mod_gzip_item_exclude mime ^image/.*
mod_gzip_item_exclude rspheader ^Content-Encoding:.*gzip.*
</IfModule>
