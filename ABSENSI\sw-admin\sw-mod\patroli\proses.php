<?php
session_start();
if(empty($_SESSION['SESSION_USER']) && empty($_SESSION['SESSION_ID'])){
    header('location:../../login/');
 exit;}
else {
require_once'../../../sw-library/sw-config.php';
require_once'../../login/login_session.php';
include('../../../sw-library/sw-function.php');
$max_size = 5000000; //5MB

switch (@$_GET['action']){

case 'add':
  $error = array();
  
  if (empty($_POST['employees_id'])) {
      $error[] = 'Karyawan tidak boleh kosong';
    } else {
      $employees_id = anti_injection($_POST['employees_id']);
  }

  if (empty($_POST['building_id'])) {
      $error[] = 'Lokasi tidak boleh kosong';
    } else {
      $building_id = anti_injection($_POST['building_id']);
  }

  if (empty($_POST['status'])) {
      $error[] = 'Status tidak boleh kosong';
    } else {
      $status = anti_injection($_POST['status']);
  }

  $rating = !empty($_POST['rating']) ? anti_injection($_POST['rating']) : NULL;
  $komentar = !empty($_POST['komentar']) ? anti_injection($_POST['komentar']) : NULL;
  $checklist_id = !empty($_POST['checklist_id']) ? anti_injection($_POST['checklist_id']) : NULL;

  // Handle file upload
  $dokumentasi = '';
  if(isset($_FILES['dokumentasi']) && $_FILES['dokumentasi']['error'] == 0) {
    $file_name = $_FILES['dokumentasi']['name'];
    $file_size = $_FILES['dokumentasi']['size'];
    $file_tmp = $_FILES['dokumentasi']['tmp_name'];
    $file_ext = strtolower(pathinfo($file_name, PATHINFO_EXTENSION));
    
    // Check file size
    if($file_size > $max_size) {
      $error[] = 'Ukuran file terlalu besar (maksimal 5MB)';
    }
    
    // Check file extension
    $allowed_ext = array('jpg', 'jpeg', 'png', 'gif', 'mp4', 'avi', 'mov', 'wmv');
    if(!in_array($file_ext, $allowed_ext)) {
      $error[] = 'Format file tidak diizinkan';
    }
    
    if(empty($error)) {
      // Create upload directory if not exists
      $upload_dir = '../../../sw-content/patroli/';
      if(!is_dir($upload_dir)) {
        mkdir($upload_dir, 0755, true);
      }
      
      // Generate unique filename
      $dokumentasi = 'patroli-' . $employees_id . '-' . time() . '.' . $file_ext;
      $upload_path = $upload_dir . $dokumentasi;
      
      if(!move_uploaded_file($file_tmp, $upload_path)) {
        $error[] = 'Gagal mengupload file';
      }
    }
  } else {
    $error[] = 'File dokumentasi wajib diupload';
  }

  if(empty($error)) {
    $rating_value = ($rating !== NULL) ? "'$rating'" : "NULL";
    $komentar_value = ($komentar !== NULL) ? "'$komentar'" : "NULL";
    $checklist_value = ($checklist_id !== NULL) ? "'$checklist_id'" : "NULL";
    
    $query = "INSERT INTO patroli (employees_id, building_id, dokumentasi, status, rating, komentar, checklist_id) 
              VALUES ('$employees_id', '$building_id', '$dokumentasi', '$status', $rating_value, $komentar_value, $checklist_value)";
    
    if($connection->query($query)) {
      $patroli_id = $connection->insert_id;
      echo '<script>
        alert("Data patroli berhasil ditambahkan!\\nID Patroli: ' . $patroli_id . '");
        window.location="../../patroli?success=add&id=' . $patroli_id . '";
      </script>';
    } else {
      echo '<script>alert("Gagal menambahkan data patroli: ' . addslashes($connection->error) . '"); window.location="../../patroli?op=add";</script>';
    }
  } else {
    $error_msg = implode('\\n', $error);
    echo '<script>alert("Error:\\n' . $error_msg . '"); window.location="../../patroli?op=add";</script>';
  }
break;

case 'edit':
  $error = array();
  
  if (empty($_POST['patroli_id'])) {
      $error[] = 'ID Patroli tidak valid';
    } else {
      $patroli_id = anti_injection($_POST['patroli_id']);
  }

  if (empty($_POST['employees_id'])) {
      $error[] = 'Karyawan tidak boleh kosong';
    } else {
      $employees_id = anti_injection($_POST['employees_id']);
  }

  if (empty($_POST['building_id'])) {
      $error[] = 'Lokasi tidak boleh kosong';
    } else {
      $building_id = anti_injection($_POST['building_id']);
  }

  if (empty($_POST['status'])) {
      $error[] = 'Status tidak boleh kosong';
    } else {
      $status = anti_injection($_POST['status']);
  }

  $rating = !empty($_POST['rating']) ? anti_injection($_POST['rating']) : NULL;
  $komentar = !empty($_POST['komentar']) ? anti_injection($_POST['komentar']) : NULL;
  $checklist_id = !empty($_POST['checklist_id']) ? anti_injection($_POST['checklist_id']) : NULL;
  $dokumentasi_lama = $_POST['dokumentasi_lama'];

  // Handle file upload (optional for edit)
  $dokumentasi = $dokumentasi_lama; // Keep old file by default
  if(isset($_FILES['dokumentasi']) && $_FILES['dokumentasi']['error'] == 0) {
    $file_name = $_FILES['dokumentasi']['name'];
    $file_size = $_FILES['dokumentasi']['size'];
    $file_tmp = $_FILES['dokumentasi']['tmp_name'];
    $file_ext = strtolower(pathinfo($file_name, PATHINFO_EXTENSION));
    
    // Check file size
    if($file_size > $max_size) {
      $error[] = 'Ukuran file terlalu besar (maksimal 5MB)';
    }
    
    // Check file extension
    $allowed_ext = array('jpg', 'jpeg', 'png', 'gif', 'mp4', 'avi', 'mov', 'wmv');
    if(!in_array($file_ext, $allowed_ext)) {
      $error[] = 'Format file tidak diizinkan';
    }
    
    if(empty($error)) {
      // Create upload directory if not exists
      $upload_dir = '../../../sw-content/patroli/';
      if(!is_dir($upload_dir)) {
        mkdir($upload_dir, 0755, true);
      }
      
      // Delete old file
      if(!empty($dokumentasi_lama)) {
        $old_file = $upload_dir . $dokumentasi_lama;
        if(file_exists($old_file)) {
          unlink($old_file);
        }
      }
      
      // Generate unique filename
      $dokumentasi = 'patroli-' . $employees_id . '-' . time() . '.' . $file_ext;
      $upload_path = $upload_dir . $dokumentasi;
      
      if(!move_uploaded_file($file_tmp, $upload_path)) {
        $error[] = 'Gagal mengupload file';
        $dokumentasi = $dokumentasi_lama; // Revert to old file
      }
    }
  }

  if(empty($error)) {
    $rating_value = ($rating !== NULL) ? "'$rating'" : "NULL";
    $komentar_value = ($komentar !== NULL) ? "'$komentar'" : "NULL";
    $checklist_value = ($checklist_id !== NULL) ? "'$checklist_id'" : "NULL";
    
    $query = "UPDATE patroli SET 
              employees_id='$employees_id', 
              building_id='$building_id', 
              dokumentasi='$dokumentasi', 
              status='$status', 
              rating=$rating_value, 
              komentar=$komentar_value, 
              checklist_id=$checklist_value 
              WHERE patroli_id='$patroli_id'";
    
    if($connection->query($query)) {
      echo '<script>
        alert("Data patroli berhasil diupdate!\\nID Patroli: ' . $patroli_id . '");
        window.location="../../patroli?success=edit&id=' . $patroli_id . '";
      </script>';
    } else {
      echo '<script>alert("Gagal mengupdate data patroli: ' . addslashes($connection->error) . '"); window.location="../../patroli?op=edit&patroli_id='.$patroli_id.'";</script>';
    }
  } else {
    $error_msg = implode('\\n', $error);
    echo '<script>alert("Error:\\n' . $error_msg . '"); window.location="../../patroli?op=edit&patroli_id='.$patroli_id.'";</script>';
  }
break;

case 'delete':
  if(!empty($_GET['patroli_id'])) {
    $patroli_id = anti_injection($_GET['patroli_id']);
    
    // Get file name to delete
    $query_file = "SELECT dokumentasi FROM patroli WHERE patroli_id='$patroli_id'";
    $result_file = $connection->query($query_file);
    $row_file = $result_file->fetch_assoc();
    
    // Delete file if exists
    if(!empty($row_file['dokumentasi'])) {
      $file_path = '../../../sw-content/patroli/'.$row_file['dokumentasi'];
      if(file_exists($file_path)) {
        unlink($file_path);
      }
    }
    
    // Delete record
    $query_delete = "DELETE FROM patroli WHERE patroli_id='$patroli_id'";
    if($connection->query($query_delete)) {
      echo '<script>
        alert("Data patroli berhasil dihapus!\\nID Patroli: ' . $patroli_id . '");
        window.location="../../patroli?success=delete&id=' . $patroli_id . '";
      </script>';
    } else {
      echo '<script>alert("Gagal menghapus data patroli!"); window.location="../../patroli";</script>';
    }
  } else {
    echo '<script>alert("ID Patroli tidak valid!"); window.location="../../patroli";</script>';
  }
break;

default:
  header('location:../../patroli');
break;

}
}
?>
