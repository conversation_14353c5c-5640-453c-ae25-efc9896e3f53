-- --------------------------------------------------------
--
-- Struktur dari tabel `patroli`
-- Tabel untuk mencatat aktivitas patroli karyawan
--
-- Dibuat pada: <?php echo date('d M Y H:i'); ?>

CREATE TABLE `patroli` (
  `patroli_id` int(11) NOT NULL AUTO_INCREMENT,
  `employees_id` int(11) NOT NULL COMMENT 'ID karyawan yang melakukan patroli',
  `building_id` int(11) NOT NULL COMMENT 'ID lokasi patroli',
  `dokumentasi` varchar(200) NOT NULL COMMENT 'File foto/video dokumentasi patroli',
  `tanggal` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT 'Waktu patroli dilakukan',
  `status` varchar(20) NOT NULL DEFAULT 'pending' COMMENT 'Status patroli: pending, approved, rejected',
  `komentar` text NULL COMMENT 'Komentar dari operator atau karyawan',
  `rating` int(1) NULL COMMENT 'Rating patroli 1-5, null jika belum dinilai',
  `checklist_id` int(11) NULL COMMENT 'ID checklist untuk operator',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`patroli_id`),
  KEY `idx_employees_id` (`employees_id`),
  KEY `idx_building_id` (`building_id`),
  KEY `idx_tanggal` (`tanggal`),
  KEY `idx_status` (`status`),
  KEY `idx_checklist_id` (`checklist_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='Tabel untuk mencatat aktivitas patroli karyawan';

-- --------------------------------------------------------
--
-- Struktur dari tabel `patroli_checklist`
-- Tabel untuk checklist yang digunakan operator
--

CREATE TABLE `patroli_checklist` (
  `checklist_id` int(11) NOT NULL AUTO_INCREMENT,
  `checklist_name` varchar(100) NOT NULL COMMENT 'Nama checklist',
  `checklist_description` text NULL COMMENT 'Deskripsi checklist',
  `is_active` tinyint(1) NOT NULL DEFAULT 1 COMMENT '1=aktif, 0=nonaktif',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`checklist_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='Tabel checklist untuk operator patroli';

-- --------------------------------------------------------
--
-- Struktur dari tabel `patroli_checklist_items`
-- Tabel untuk item-item dalam checklist
--

CREATE TABLE `patroli_checklist_items` (
  `item_id` int(11) NOT NULL AUTO_INCREMENT,
  `checklist_id` int(11) NOT NULL COMMENT 'ID checklist',
  `item_name` varchar(150) NOT NULL COMMENT 'Nama item checklist',
  `item_description` text NULL COMMENT 'Deskripsi item',
  `is_required` tinyint(1) NOT NULL DEFAULT 1 COMMENT '1=wajib, 0=opsional',
  `sort_order` int(3) NOT NULL DEFAULT 0 COMMENT 'Urutan tampil',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`item_id`),
  KEY `idx_checklist_id` (`checklist_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='Item-item dalam checklist patroli';

-- --------------------------------------------------------
--
-- Struktur dari tabel `patroli_checklist_responses`
-- Tabel untuk jawaban checklist dari karyawan
--

CREATE TABLE `patroli_checklist_responses` (
  `response_id` int(11) NOT NULL AUTO_INCREMENT,
  `patroli_id` int(11) NOT NULL COMMENT 'ID patroli',
  `item_id` int(11) NOT NULL COMMENT 'ID item checklist',
  `response_value` varchar(10) NOT NULL COMMENT 'Jawaban: yes, no, na (not applicable)',
  `response_note` text NULL COMMENT 'Catatan tambahan',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`response_id`),
  KEY `idx_patroli_id` (`patroli_id`),
  KEY `idx_item_id` (`item_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='Jawaban checklist patroli dari karyawan';

-- --------------------------------------------------------
--
-- Data awal untuk tabel `patroli_checklist`
--

INSERT INTO `patroli_checklist` (`checklist_name`, `checklist_description`, `is_active`) VALUES
('Checklist Patroli Standar', 'Checklist standar untuk patroli rutin', 1),
('Checklist Patroli Keamanan', 'Checklist khusus untuk patroli keamanan', 1),
('Checklist Patroli Kebersihan', 'Checklist untuk patroli kebersihan area', 1);

-- --------------------------------------------------------
--
-- Data awal untuk tabel `patroli_checklist_items`
--

INSERT INTO `patroli_checklist_items` (`checklist_id`, `item_name`, `item_description`, `is_required`, `sort_order`) VALUES
-- Checklist Standar (ID: 1)
(1, 'Kondisi Pintu Masuk', 'Periksa kondisi pintu masuk utama', 1, 1),
(1, 'Penerangan Area', 'Periksa kondisi lampu dan penerangan', 1, 2),
(1, 'Kebersihan Area', 'Periksa kebersihan area patroli', 1, 3),
(1, 'Kondisi Peralatan', 'Periksa kondisi peralatan di area', 0, 4),

-- Checklist Keamanan (ID: 2)
(2, 'Sistem CCTV', 'Periksa kondisi dan fungsi CCTV', 1, 1),
(2, 'Akses Kontrol', 'Periksa sistem akses kontrol', 1, 2),
(2, 'Area Parkir', 'Periksa keamanan area parkir', 1, 3),
(2, 'Pagar dan Gerbang', 'Periksa kondisi pagar dan gerbang', 1, 4),

-- Checklist Kebersihan (ID: 3)
(3, 'Tempat Sampah', 'Periksa kondisi dan isi tempat sampah', 1, 1),
(3, 'Toilet/WC', 'Periksa kebersihan toilet', 1, 2),
(3, 'Area Makan', 'Periksa kebersihan area makan/kantin', 0, 3),
(3, 'Lantai dan Dinding', 'Periksa kebersihan lantai dan dinding', 1, 4);

-- --------------------------------------------------------
--
-- Foreign Key Constraints
--

-- Tambahkan foreign key untuk tabel patroli
ALTER TABLE `patroli`
  ADD CONSTRAINT `fk_patroli_employees` FOREIGN KEY (`employees_id`) REFERENCES `employees` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
  ADD CONSTRAINT `fk_patroli_building` FOREIGN KEY (`building_id`) REFERENCES `building` (`building_id`) ON DELETE CASCADE ON UPDATE CASCADE,
  ADD CONSTRAINT `fk_patroli_checklist` FOREIGN KEY (`checklist_id`) REFERENCES `patroli_checklist` (`checklist_id`) ON DELETE SET NULL ON UPDATE CASCADE;

-- Tambahkan foreign key untuk tabel patroli_checklist_items
ALTER TABLE `patroli_checklist_items`
  ADD CONSTRAINT `fk_checklist_items_checklist` FOREIGN KEY (`checklist_id`) REFERENCES `patroli_checklist` (`checklist_id`) ON DELETE CASCADE ON UPDATE CASCADE;

-- Tambahkan foreign key untuk tabel patroli_checklist_responses
ALTER TABLE `patroli_checklist_responses`
  ADD CONSTRAINT `fk_responses_patroli` FOREIGN KEY (`patroli_id`) REFERENCES `patroli` (`patroli_id`) ON DELETE CASCADE ON UPDATE CASCADE,
  ADD CONSTRAINT `fk_responses_item` FOREIGN KEY (`item_id`) REFERENCES `patroli_checklist_items` (`item_id`) ON DELETE CASCADE ON UPDATE CASCADE;

-- --------------------------------------------------------
--
-- AUTO_INCREMENT untuk tabel yang baru dibuat
--

ALTER TABLE `patroli`
  MODIFY `patroli_id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=1;

ALTER TABLE `patroli_checklist`
  MODIFY `checklist_id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=4;

ALTER TABLE `patroli_checklist_items`
  MODIFY `item_id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=13;

ALTER TABLE `patroli_checklist_responses`
  MODIFY `response_id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=1;

-- --------------------------------------------------------
--
-- Contoh data untuk testing (opsional)
--

-- INSERT INTO `patroli` (`employees_id`, `building_id`, `dokumentasi`, `status`, `komentar`, `rating`, `checklist_id`) VALUES
-- (17, 7, 'patroli-17-' + UNIX_TIMESTAMP() + '.jpg', 'pending', 'Patroli rutin pagi hari', NULL, 1),
-- (18, 8, 'patroli-18-' + UNIX_TIMESTAMP() + '.jpg', 'approved', 'Patroli keamanan malam', 4, 2);

COMMIT;
