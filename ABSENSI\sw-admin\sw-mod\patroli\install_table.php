<?php
// File untuk menginstall tabel patroli jika belum ada
session_start();
if(empty($_SESSION['SESSION_USER']) && empty($_SESSION['SESSION_ID'])){
    header('location:../../login/');
    exit;
}

require_once'../../../sw-library/sw-config.php';
require_once'../../login/login_session.php';

// Check if user is admin
if($level_user != 1) {
    echo '<script>alert("Akses ditolak! Hanya admin yang dapat menginstall tabel."); window.location="../../";</script>';
    exit;
}

// Check if table already exists
$check_table = "SHOW TABLES LIKE 'patroli'";
$result_check = $connection->query($check_table);

if($result_check->num_rows > 0) {
    echo '<script>alert("Tabel patroli sudah ada!"); window.location="../../patroli";</script>';
    exit;
}

// Create table patroli
$create_table = "
CREATE TABLE `patroli` (
  `patroli_id` int(11) NOT NULL AUTO_INCREMENT,
  `employees_id` int(11) NOT NULL COMMENT 'ID karyawan yang melakukan patroli',
  `building_id` int(11) NOT NULL COMMENT 'ID lokasi patroli',
  `dokumentasi` varchar(200) NOT NULL COMMENT 'File foto/video dokumentasi patroli',
  `tanggal` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT 'Tanggal dan waktu patroli dilakukan',
  `status` varchar(20) NOT NULL DEFAULT 'pending' COMMENT 'Status patroli: pending, approved, rejected, completed',
  `komentar` text NULL COMMENT 'Komentar dari operator atau karyawan',
  `rating` int(1) NULL COMMENT 'Rating patroli 1-5, null jika belum dinilai',
  `checklist_id` int(11) NULL COMMENT 'ID checklist untuk bagian operator',
  PRIMARY KEY (`patroli_id`),
  KEY `idx_employees_id` (`employees_id`),
  KEY `idx_building_id` (`building_id`),
  KEY `idx_tanggal` (`tanggal`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
";

// Add foreign key constraints
$add_constraints = "
ALTER TABLE `patroli`
  ADD CONSTRAINT `fk_patroli_employees` FOREIGN KEY (`employees_id`) REFERENCES `employees` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
  ADD CONSTRAINT `fk_patroli_building` FOREIGN KEY (`building_id`) REFERENCES `building` (`building_id`) ON DELETE CASCADE ON UPDATE CASCADE;
";

// Execute queries
$success = true;
$error_msg = '';

if(!$connection->query($create_table)) {
    $success = false;
    $error_msg = 'Error creating table: ' . $connection->error;
}

if($success && !$connection->query($add_constraints)) {
    $success = false;
    $error_msg = 'Error adding constraints: ' . $connection->error;
}

// Create upload directory
$upload_dir = '../../../sw-content/patroli/';
if(!is_dir($upload_dir)) {
    if(!mkdir($upload_dir, 0755, true)) {
        $success = false;
        $error_msg = 'Error creating upload directory';
    } else {
        // Create index.html for security
        $index_content = '<!DOCTYPE html>
<html>
<head>
    <title>403 Forbidden</title>
</head>
<body>
    <h1>Directory access is forbidden.</h1>
</body>
</html>';
        file_put_contents($upload_dir . 'index.html', $index_content);
    }
}

if($success) {
    echo '<script>alert("Tabel patroli berhasil dibuat!\\nSekarang Anda dapat menggunakan fitur patroli."); window.location="../../patroli";</script>';
} else {
    echo '<script>alert("Gagal membuat tabel patroli:\\n' . $error_msg . '"); window.location="../../";</script>';
}
?>
