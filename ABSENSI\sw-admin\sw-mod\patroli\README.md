# 🛡️ Modul Patroli - Admin Panel

## 📋 Deskripsi
Modul patroli telah berhasil ditambahkan ke admin panel sistem absensi SIGAP. Fitur ini memungkinkan admin dan operator untuk mengelola data patroli karyawan.

## ✨ Fitur yang Tersedia

### 🔐 **Aks<PERSON>:**
- **Administrator (Level 1)**: <PERSON><PERSON><PERSON> (CRUD)
- **Operator (Level 2)**: <PERSON><PERSON><PERSON> (CRUD)
- **User lain**: Tidak ada akses

### 📊 **Fitur Utama:**
1. **Daftar Patroli** - Menampilkan semua data patroli dengan DataTable
2. **Tambah Patroli** - Form untuk menambah data patroli baru
3. **Edit Patroli** - Form untuk mengubah data patroli
4. **Hapus Patroli** - Menghapus data patroli dan file dokumentasi
5. **Upload Dokumentasi** - Upload foto/video dokumentasi patroli
6. **Rating System** - Sistem penilaian dengan bintang (1-5)
7. **Status Management** - Kelola status patroli (Menunggu, Disetujui, Di<PERSON>lak, Selesai)

## 🗂️ Struktur File

```
ABSENSI/sw-admin/sw-mod/patroli/
├── patroli.php          # File utama modul patroli
├── proses.php           # Handler untuk operasi CRUD
├── scripts.js           # JavaScript untuk interaksi UI
├── install_table.php    # Script untuk install tabel (jika diperlukan)
└── README.md           # Dokumentasi ini
```

## 🎨 **Tampilan & UI:**

### **Daftar Patroli:**
- Tabel responsif dengan DataTable
- Kolom: No, Karyawan, Lokasi, Tanggal, Status, Rating, Dokumentasi, Aksi
- Status dengan badge berwarna
- Rating dengan icon bintang ⭐
- Tombol aksi (Edit/Hapus)

### **Form Input:**
- Dropdown karyawan dan lokasi
- Upload file dokumentasi (foto/video)
- Dropdown status dengan bahasa Indonesia
- Rating dengan preview bintang
- Textarea untuk komentar

### **Rating System:**
- Visual: ⭐⭐⭐⭐⭐ (1-5 bintang)
- Preview real-time saat memilih rating
- Tampilan bintang kuning untuk rating aktif

## 🔧 **Instalasi & Setup:**

### **1. Database:**
Tabel `patroli` sudah ditambahkan ke `absensi_sigap.sql` dengan struktur:
```sql
CREATE TABLE `patroli` (
  `patroli_id` int(11) NOT NULL AUTO_INCREMENT,
  `employees_id` int(11) NOT NULL,
  `building_id` int(11) NOT NULL,
  `dokumentasi` varchar(200) NOT NULL,
  `tanggal` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `status` varchar(20) NOT NULL DEFAULT 'pending',
  `komentar` text NULL,
  `rating` int(1) NULL,
  `checklist_id` int(11) NULL,
  PRIMARY KEY (`patroli_id`)
);
```

### **2. Menu Navigation:**
Menu "Data Patroli" sudah ditambahkan ke `sw-panel.php` dengan icon shield.

### **3. Upload Directory:**
Folder `ABSENSI/sw-content/patroli/` dibuat untuk menyimpan file dokumentasi.

## 🚀 **Cara Menggunakan:**

### **Akses Menu:**
1. Login sebagai Admin atau Operator
2. Klik menu "Data Patroli" di sidebar
3. Akan muncul halaman daftar patroli

### **Tambah Patroli Baru:**
1. Klik tombol "Tambah Baru"
2. Pilih karyawan dan lokasi
3. Upload file dokumentasi (foto/video)
4. Pilih status dan rating
5. Tambahkan komentar (opsional)
6. Klik "Simpan"

### **Edit Patroli:**
1. Klik tombol "Edit" (icon pensil kuning)
2. Ubah data yang diperlukan
3. Upload file baru (opsional)
4. Klik "Update"

### **Hapus Patroli:**
1. Klik tombol "Hapus" (icon trash merah)
2. Konfirmasi penghapusan
3. Data dan file akan terhapus

## 📁 **Upload File:**

### **Format yang Didukung:**
- **Gambar**: JPG, JPEG, PNG, GIF
- **Video**: MP4, AVI, MOV, WMV
- **Ukuran Maksimal**: 5MB

### **Penamaan File:**
Format: `patroli-{employee_id}-{timestamp}.{extension}`
Contoh: `patroli-17-1625123456.jpg`

## 🎯 **Status Patroli:**

| Status | Badge | Keterangan |
|--------|-------|------------|
| `pending` | 🟡 Menunggu | Patroli baru, menunggu verifikasi |
| `approved` | 🟢 Disetujui | Patroli telah disetujui |
| `rejected` | 🔴 Ditolak | Patroli ditolak |
| `completed` | 🔵 Selesai | Patroli selesai dan dinilai |

## ⭐ **Rating System:**

| Rating | Tampilan | Keterangan |
|--------|----------|------------|
| 1 | ⭐ | Sangat Buruk |
| 2 | ⭐⭐ | Buruk |
| 3 | ⭐⭐⭐ | Cukup |
| 4 | ⭐⭐⭐⭐ | Baik |
| 5 | ⭐⭐⭐⭐⭐ | Sangat Baik |
| NULL | - | Belum dinilai |

## 🔒 **Keamanan:**

1. **Akses Control**: Hanya admin dan operator yang dapat mengakses
2. **File Validation**: Validasi tipe dan ukuran file
3. **SQL Injection Protection**: Menggunakan `anti_injection()`
4. **Directory Protection**: File `index.html` di folder upload
5. **Foreign Key Constraints**: Menjaga integritas data

## 🐛 **Troubleshooting:**

### **Tabel Tidak Ada:**
Jalankan: `http://localhost/ABSENSI%20SIGAP/ABSENSI/sw-admin/sw-mod/patroli/install_table.php`

### **Upload Gagal:**
- Periksa permission folder `sw-content/patroli/`
- Pastikan ukuran file < 5MB
- Periksa format file yang didukung

### **Menu Tidak Muncul:**
- Pastikan login sebagai admin/operator
- Clear browser cache
- Periksa file `sw-panel.php`

## 📞 **Support:**

Jika ada masalah atau pertanyaan:
1. Periksa error log PHP
2. Periksa console browser untuk JavaScript error
3. Pastikan database connection berjalan
4. Hubungi administrator sistem

---
**Dibuat pada**: <?php echo date('d M Y H:i'); ?>  
**Versi**: 1.0  
**Kompatibel dengan**: PHP 7.4+, MySQL 5.7+
