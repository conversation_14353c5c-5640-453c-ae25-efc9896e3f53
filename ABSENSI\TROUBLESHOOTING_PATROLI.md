# 🔧 Troubleshooting - Fitur Patroli

## ❌ Error: DataTables warning: table id=sw-datatable - Requested unknown parameter '1' for row 0

### 🔍 **Penyebab:**
Error ini terjadi karena:
1. Tabel `patroli` belum dibuat di database
2. Tidak ada data di tabel patroli
3. Query database gagal
4. Struktur tabel tidak sesuai

### ✅ **Solusi:**

#### **1. Install Tabel Patroli**
Jalankan salah satu cara berikut:

**Cara A - Auto Install (Recommended):**
```
http://localhost/ABSENSI%20SIGAP/ABSENSI/install_patroli.php
```

**Cara B - Manual via phpMyAdmin:**
1. Buka phpMyAdmin
2. Pilih database `absensi_sigap`
3. Klik tab "SQL"
4. Copy-paste SQL berikut:

```sql
CREATE TABLE `patroli` (
  `patroli_id` int(11) NOT NULL AUTO_INCREMENT,
  `employees_id` int(11) NOT NULL COMMENT 'ID karyawan yang melakukan patroli',
  `building_id` int(11) NOT NULL COMMENT 'ID lokasi patroli',
  `dokumentasi` varchar(200) NOT NULL COMMENT 'File foto/video dokumentasi patroli',
  `tanggal` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT 'Tanggal dan waktu patroli dilakukan',
  `status` varchar(20) NOT NULL DEFAULT 'pending' COMMENT 'Status patroli: pending, approved, rejected, completed',
  `komentar` text NULL COMMENT 'Komentar dari operator atau karyawan',
  `rating` int(1) NULL COMMENT 'Rating patroli 1-5, null jika belum dinilai',
  `checklist_id` int(11) NULL COMMENT 'ID checklist untuk bagian operator',
  PRIMARY KEY (`patroli_id`),
  KEY `idx_employees_id` (`employees_id`),
  KEY `idx_building_id` (`building_id`),
  KEY `idx_tanggal` (`tanggal`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

ALTER TABLE `patroli`
  ADD CONSTRAINT `fk_patroli_employees` FOREIGN KEY (`employees_id`) REFERENCES `employees` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
  ADD CONSTRAINT `fk_patroli_building` FOREIGN KEY (`building_id`) REFERENCES `building` (`building_id`) ON DELETE CASCADE ON UPDATE CASCADE;
```

**Cara C - Import Database Lengkap:**
1. Backup database lama
2. Import file `ABSENSI/DB/absensi_sigap.sql` (sudah include tabel patroli)

#### **2. Buat Folder Upload**
Pastikan folder ini ada:
```
ABSENSI/sw-content/patroli/
```

Jika belum ada, buat manual atau jalankan script install.

#### **3. Periksa Permission**
Pastikan folder `sw-content/patroli/` memiliki permission write (755 atau 777).

#### **4. Clear Browser Cache**
1. Tekan Ctrl+F5 untuk hard refresh
2. Atau clear cache browser
3. Restart browser

---

## 🚫 **Error Lainnya:**

### **Menu Patroli Tidak Muncul**
**Solusi:**
- Login sebagai Administrator atau Operator
- User biasa tidak memiliki akses ke menu patroli

### **Upload File Gagal**
**Solusi:**
- Periksa ukuran file (max 5MB)
- Periksa format file (JPG, PNG, GIF, MP4, AVI, MOV, WMV)
- Periksa permission folder upload

### **Rating Tidak Muncul**
**Solusi:**
- Pastikan JavaScript aktif di browser
- Periksa console browser untuk error
- Refresh halaman

### **Status Tidak Tersimpan**
**Solusi:**
- Periksa koneksi database
- Periksa log error PHP
- Pastikan field status tidak kosong

---

## 🔍 **Debugging:**

### **Cek Tabel Database:**
```sql
SHOW TABLES LIKE 'patroli';
DESCRIBE patroli;
SELECT COUNT(*) FROM patroli;
```

### **Cek Error Log:**
- PHP Error Log: `/xampp/apache/logs/error.log`
- Browser Console: F12 → Console tab

### **Test Query Manual:**
```sql
SELECT p.*, e.employees_name, b.name as building_name 
FROM patroli p 
LEFT JOIN employees e ON p.employees_id = e.id 
LEFT JOIN building b ON p.building_id = b.building_id 
ORDER BY p.tanggal DESC;
```

---

## 📞 **Bantuan Lebih Lanjut:**

Jika masalah masih berlanjut:

1. **Periksa Konfigurasi Database:**
   - File: `ABSENSI/sw-library/sw-config.php`
   - Pastikan koneksi database benar

2. **Periksa Versi PHP:**
   - Minimum PHP 7.4
   - Ekstensi mysqli aktif

3. **Periksa Versi MySQL:**
   - Minimum MySQL 5.7 atau MariaDB 10.2

4. **Contact Support:**
   - Sertakan screenshot error
   - Sertakan log error PHP
   - Jelaskan langkah yang sudah dicoba

---

## ✅ **Checklist Instalasi:**

- [ ] Database `absensi_sigap` ada
- [ ] Tabel `patroli` sudah dibuat
- [ ] Foreign key constraints ditambahkan
- [ ] Folder `sw-content/patroli/` ada dan writable
- [ ] Menu "Data Patroli" muncul di admin panel
- [ ] Login sebagai admin/operator
- [ ] JavaScript aktif di browser
- [ ] Tidak ada error di console browser

---

**Update Terakhir:** <?php echo date('d M Y H:i'); ?>  
**File:** TROUBLESHOOTING_PATROLI.md
