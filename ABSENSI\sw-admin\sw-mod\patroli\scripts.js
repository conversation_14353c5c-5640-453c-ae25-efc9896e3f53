$(document).ready(function() {
    // Initialize DataTable
    $('#sw-datatable').DataTable({
        "responsive": true,
        "autoWidth": false,
        "language": {
            "url": "//cdn.datatables.net/plug-ins/1.10.24/i18n/Indonesian.json"
        },
        "order": [[ 3, "desc" ]], // Order by date column (index 3) descending
        "columnDefs": [
            { "orderable": false, "targets": [6, 7] }, // Disable ordering for dokumentasi and action columns
            { "searchable": false, "targets": [7] } // Disable search for action column
        ]
    });

    // File upload preview
    $('input[type="file"]').change(function() {
        var file = this.files[0];
        if (file) {
            var fileSize = file.size;
            var maxSize = 5 * 1024 * 1024; // 5MB
            
            if (fileSize > maxSize) {
                alert('Ukuran file terlalu besar! Maksimal 5MB.');
                $(this).val('');
                return false;
            }
            
            // Show file info
            var fileName = file.name;
            var fileType = file.type;
            var fileInfo = '<div class="alert alert-info mt-2">';
            fileInfo += '<strong>File dipilih:</strong> ' + fileName + '<br>';
            fileInfo += '<strong>Ukuran:</strong> ' + (fileSize / 1024 / 1024).toFixed(2) + ' MB<br>';
            fileInfo += '<strong>Tipe:</strong> ' + fileType;
            fileInfo += '</div>';
            
            // Remove existing file info
            $(this).parent().find('.alert-info').remove();
            // Add new file info
            $(this).parent().append(fileInfo);
        }
    });

    // Rating select change handler
    $('#rating-select, #rating-select-edit').change(function() {
        var rating = $(this).val();
        var previewDiv = $(this).siblings('.rating-preview, .rating-preview-edit');
        var starsSpan = previewDiv.find('.rating-stars');

        if (rating) {
            var starsHtml = '';
            for (var i = 1; i <= 5; i++) {
                if (i <= rating) {
                    starsHtml += '<i class="fa fa-star" style="color: #f39c12; font-size: 18px;"></i> ';
                } else {
                    starsHtml += '<i class="fa fa-star-o" style="color: #ddd; font-size: 18px;"></i> ';
                }
            }
            starsSpan.html(starsHtml);
            previewDiv.show();
        } else {
            previewDiv.hide();
        }
    });

    // Initialize rating preview on page load
    $('#rating-select, #rating-select-edit').trigger('change');

    // Status change confirmation
    $('select[name="status"]').change(function() {
        var status = $(this).val();
        var confirmMessage = '';
        
        switch(status) {
            case 'approved':
                confirmMessage = 'Yakin ingin menyetujui patroli ini?';
                break;
            case 'rejected':
                confirmMessage = 'Yakin ingin menolak patroli ini?';
                break;
            case 'completed':
                confirmMessage = 'Yakin menandai patroli ini sebagai selesai?';
                break;
        }
        
        if (confirmMessage && !confirm(confirmMessage)) {
            // Reset to previous value if user cancels
            $(this).val($(this).data('previous-value') || 'pending');
        } else {
            // Store current value as previous
            $(this).data('previous-value', status);
        }
    });

    // Initialize previous values for status selects
    $('select[name="status"]').each(function() {
        $(this).data('previous-value', $(this).val());
    });

    // Form validation
    $('form').submit(function(e) {
        var isValid = true;
        var errorMessage = '';
        
        // Check required fields
        $(this).find('[required]').each(function() {
            if (!$(this).val()) {
                isValid = false;
                var label = $(this).closest('.form-group').find('label').text().replace('*', '');
                errorMessage += '- ' + label + ' tidak boleh kosong\n';
                $(this).addClass('error');
            } else {
                $(this).removeClass('error');
            }
        });
        
        // Check file upload for add form
        if ($(this).find('input[name="dokumentasi"]').prop('required') && 
            !$(this).find('input[name="dokumentasi"]').val()) {
            isValid = false;
            errorMessage += '- File dokumentasi wajib diupload\n';
        }
        
        if (!isValid) {
            alert('Mohon lengkapi data berikut:\n' + errorMessage);
            e.preventDefault();
            return false;
        }
        
        return true;
    });

    // Add error styling
    $('<style>')
        .prop('type', 'text/css')
        .html('.error { border-color: #dd4b39 !important; }')
        .appendTo('head');

    // Auto-hide alerts after 5 seconds
    setTimeout(function() {
        $('.alert').fadeOut('slow');
    }, 5000);

    // Confirm delete action
    $('.btn-danger').click(function(e) {
        if ($(this).attr('href') && $(this).attr('href').includes('delete')) {
            if (!confirm('Yakin ingin menghapus data patroli ini?\nData yang dihapus tidak dapat dikembalikan.')) {
                e.preventDefault();
                return false;
            }
        }
    });

    // Image/video preview modal (for future enhancement)
    $('.preview-media').click(function(e) {
        e.preventDefault();
        var src = $(this).attr('href');
        var type = $(this).data('type') || 'image';
        
        var modal = '<div class="modal fade" id="mediaModal" tabindex="-1">';
        modal += '<div class="modal-dialog modal-lg">';
        modal += '<div class="modal-content">';
        modal += '<div class="modal-header">';
        modal += '<button type="button" class="close" data-dismiss="modal">&times;</button>';
        modal += '<h4 class="modal-title">Preview Dokumentasi</h4>';
        modal += '</div>';
        modal += '<div class="modal-body text-center">';
        
        if (type === 'video') {
            modal += '<video controls style="max-width: 100%; max-height: 400px;">';
            modal += '<source src="' + src + '" type="video/mp4">';
            modal += 'Browser Anda tidak mendukung video.';
            modal += '</video>';
        } else {
            modal += '<img src="' + src + '" style="max-width: 100%; max-height: 400px;" alt="Dokumentasi">';
        }
        
        modal += '</div>';
        modal += '</div>';
        modal += '</div>';
        modal += '</div>';
        
        // Remove existing modal
        $('#mediaModal').remove();
        // Add new modal
        $('body').append(modal);
        // Show modal
        $('#mediaModal').modal('show');
    });
});
